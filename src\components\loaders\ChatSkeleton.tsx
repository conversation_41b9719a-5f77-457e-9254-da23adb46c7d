/**
 * ChatSkeleton Component
 * 
 * Skeleton loader for the chat section that matches the original layout:
 * - Large chat area with placeholder for chat interface
 * - Matches the dark background and rounded corners
 */

const ChatSkeleton = () => {
  return (
    <div className="col-span-3 bg-[#17074C] rounded-xl p-6 flex flex-col">
      {/* Chat Header Skeleton */}
      <div className="flex items-center justify-between mb-4">
        <div className="w-32 h-6 bg-white/20 rounded animate-pulse" />
        <div className="w-8 h-8 bg-white/20 rounded-full animate-pulse" />
      </div>

      {/* Chat Messages Area Skeleton */}
      <div className="flex-1 space-y-4 mb-4">
        {/* Message bubbles skeleton */}
        {[1, 2, 3].map((item) => (
          <div key={item} className="flex flex-col space-y-2">
            {/* User message */}
            <div className="flex justify-end">
              <div className="w-48 h-12 bg-white/20 rounded-lg animate-pulse" />
            </div>
            {/* Bot response */}
            <div className="flex justify-start">
              <div className="w-64 h-16 bg-white/10 rounded-lg animate-pulse" />
            </div>
          </div>
        ))}
      </div>

      {/* Chat Input Area Skeleton */}
      <div className="flex items-center space-x-3">
        <div className="flex-1 h-12 bg-white/20 rounded-full animate-pulse" />
        <div className="w-12 h-12 bg-white/20 rounded-full animate-pulse" />
      </div>
    </div>
  );
};

export default ChatSkeleton;

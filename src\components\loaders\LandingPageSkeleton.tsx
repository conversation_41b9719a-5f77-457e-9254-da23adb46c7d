import PrimaryFilterSkeleton from './PrimaryFilterSkeleton';
import ChatSkeleton from './ChatSkeleton';
import RecommendationSkeleton from './RecommendationSkeleton';

/**
 * LandingPageSkeleton Component
 * 
 * Complete skeleton loader for the Landing Page that matches the original layout:
 * - Welcome message and title
 * - Primary filter section
 * - Grid layout with chat and recommendations
 * - Proper spacing and responsive design
 */

const LandingPageSkeleton = () => {
  return (
    <div className="p-5 rounded-tl-xl h-[calc(100vh-73px)] flex flex-col">
      {/* Header Section Skeleton */}
      <div className="mb-4">
        {/* Welcome message skeleton */}
        <div className="w-40 h-4 bg-gray-200 rounded animate-pulse mb-2" />
        
        {/* Title skeleton */}
        <div className="flex items-center space-x-2">
          <div className="w-32 h-8 bg-gradient-to-r from-gray-200 to-gray-300 rounded animate-pulse" />
          <div className="w-48 h-8 bg-gray-200 rounded animate-pulse" />
        </div>
      </div>
      
      {/* Primary Filter Section */}
      <div className="py-4">
        <PrimaryFilterSkeleton />
      </div>
      
      {/* Main Content Grid */}
      <div className="grid grid-cols-5 gap-4 flex-1 min-h-0">
        {/* Chat Section Skeleton */}
        <ChatSkeleton />
        
        {/* Recommendations Section Skeleton */}
        <div className="col-span-2">
          <RecommendationSkeleton cardCount={3} />
        </div>
      </div>
    </div>
  );
};

export default LandingPageSkeleton;

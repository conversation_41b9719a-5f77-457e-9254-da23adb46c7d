import PrimaryFilterSkeleton from './PrimaryFilterSkeleton';
import ChatSkeleton from './ChatSkeleton';
import RecommendationSkeleton from './RecommendationSkeleton';

/**
 * LandingPageSkeleton Component
 *
 * Complete skeleton loader for the Landing Page that matches the original layout:
 * - Welcome message and title
 * - Primary filter section
 * - Responsive grid layout with chat and recommendations
 * - Proper spacing and adaptive design for all screen sizes
 */

const LandingPageSkeleton = () => {
  return (
    <div className="p-3 sm:p-5 rounded-tl-xl h-[calc(100vh-73px)] flex flex-col">
      {/* Header Section Skeleton */}
      <div className="mb-3 sm:mb-4">
        {/* Welcome message skeleton */}
        <div className="w-32 sm:w-40 h-3 sm:h-4 bg-gray-200 rounded animate-pulse mb-2" />

        {/* Title skeleton */}
        <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-2">
          <div className="w-24 sm:w-32 h-6 sm:h-8 bg-gradient-to-r from-gray-200 to-gray-300 rounded animate-pulse" />
          <div className="w-40 sm:w-48 h-6 sm:h-8 bg-gray-200 rounded animate-pulse" />
        </div>
      </div>

      {/* Primary Filter Section */}
      <div className="py-3 sm:py-4">
        <PrimaryFilterSkeleton />
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-5 gap-3 sm:gap-4 flex-1 min-h-0">
        {/* Chat Section Skeleton */}
        <div className="lg:col-span-3 order-2 lg:order-1">
          <ChatSkeleton />
        </div>

        {/* Recommendations Section Skeleton */}
        <div className="lg:col-span-2 order-1 lg:order-2">
          <RecommendationSkeleton cardCount={3} />
        </div>
      </div>
    </div>
  );
};

export default LandingPageSkeleton;

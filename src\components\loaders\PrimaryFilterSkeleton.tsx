/**
 * PrimaryFilterSkeleton Component
 * 
 * Skeleton loader for the PrimaryFilter component that matches the original layout:
 * - 5-column grid layout
 * - Each column has a label and input field with icon
 * - Responsive design with borders between columns
 */

const PrimaryFilterSkeleton = () => {
  return (
    <div className="grid grid-cols-5 grid-flow-row w-full bg-white rounded-xl p-4">
      {/* First Column */}
      <div>
        {/* Label Skeleton */}
        <div className="w-16 h-4 bg-gray-200 rounded animate-pulse mb-2" />
        <div className="flex items-center space-x-2 mt-1.5">
          {/* Icon Skeleton */}
          <div className="w-5 h-5 bg-gray-200 rounded animate-pulse" />
          {/* Input Text Skeleton */}
          <div className="w-20 h-4 bg-gray-200 rounded animate-pulse" />
        </div>
      </div>

      {/* Second Column */}
      <div className="border-l-2 pl-4">
        {/* Label Skeleton */}
        <div className="w-16 h-4 bg-gray-200 rounded animate-pulse mb-2" />
        <div className="flex items-center space-x-2 mt-1.5">
          {/* Icon Skeleton */}
          <div className="w-5 h-5 bg-gray-200 rounded animate-pulse" />
          {/* Input Text Skeleton */}
          <div className="w-20 h-4 bg-gray-200 rounded animate-pulse" />
        </div>
      </div>

      {/* Third Column */}
      <div className="border-l-2 pl-4">
        {/* Label Skeleton */}
        <div className="w-16 h-4 bg-gray-200 rounded animate-pulse mb-2" />
        <div className="flex items-center space-x-2 mt-1.5">
          {/* Icon Skeleton */}
          <div className="w-5 h-5 bg-gray-200 rounded animate-pulse" />
          {/* Input Text Skeleton */}
          <div className="w-20 h-4 bg-gray-200 rounded animate-pulse" />
        </div>
      </div>

      {/* Fourth Column */}
      <div className="border-l-2 pl-4">
        {/* Label Skeleton */}
        <div className="w-16 h-4 bg-gray-200 rounded animate-pulse mb-2" />
        <div className="flex items-center space-x-2 mt-1.5">
          {/* Icon Skeleton */}
          <div className="w-5 h-5 bg-gray-200 rounded animate-pulse" />
          {/* Input Text Skeleton */}
          <div className="w-20 h-4 bg-gray-200 rounded animate-pulse" />
        </div>
      </div>

      {/* Fifth Column */}
      <div className="border-l-2 pl-4">
        {/* Label Skeleton */}
        <div className="w-16 h-4 bg-gray-200 rounded animate-pulse mb-2" />
        <div className="flex items-center space-x-2 mt-1.5">
          {/* Icon Skeleton */}
          <div className="w-5 h-5 bg-gray-200 rounded animate-pulse" />
          {/* Input Text Skeleton */}
          <div className="w-20 h-4 bg-gray-200 rounded animate-pulse" />
        </div>
      </div>
    </div>
  );
};

export default PrimaryFilterSkeleton;

/**
 * PrimaryFilterSkeleton Component
 *
 * Skeleton loader for the PrimaryFilter component that matches the original layout:
 * - Responsive grid layout (5 columns on desktop, stacked on mobile)
 * - Each column has a label and input field with icon
 * - Proper height and spacing matching original component
 */

const PrimaryFilterSkeleton = () => {
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4 lg:gap-0 w-full bg-white rounded-xl p-4 min-h-[80px]">
      {/* First Column */}
      <div className="lg:pr-4">
        {/* Label Skeleton */}
        <div className="w-16 h-4 bg-gray-200 rounded animate-pulse mb-2" />
        <div className="flex items-center space-x-2 mt-1.5">
          {/* Icon Skeleton */}
          <div className="w-5 h-5 bg-gray-200 rounded animate-pulse flex-shrink-0" />
          {/* Input Text Skeleton */}
          <div className="w-20 h-4 bg-gray-200 rounded animate-pulse" />
        </div>
      </div>

      {/* Second Column */}
      <div className="lg:border-l-2 lg:border-gray-200 lg:pl-4 lg:pr-4">
        {/* Label Skeleton */}
        <div className="w-16 h-4 bg-gray-200 rounded animate-pulse mb-2" />
        <div className="flex items-center space-x-2 mt-1.5">
          {/* Icon Skeleton */}
          <div className="w-5 h-5 bg-gray-200 rounded animate-pulse flex-shrink-0" />
          {/* Input Text Skeleton */}
          <div className="w-20 h-4 bg-gray-200 rounded animate-pulse" />
        </div>
      </div>

      {/* Third Column */}
      <div className="lg:border-l-2 lg:border-gray-200 lg:pl-4 lg:pr-4">
        {/* Label Skeleton */}
        <div className="w-16 h-4 bg-gray-200 rounded animate-pulse mb-2" />
        <div className="flex items-center space-x-2 mt-1.5">
          {/* Icon Skeleton */}
          <div className="w-5 h-5 bg-gray-200 rounded animate-pulse flex-shrink-0" />
          {/* Input Text Skeleton */}
          <div className="w-20 h-4 bg-gray-200 rounded animate-pulse" />
        </div>
      </div>

      {/* Fourth Column */}
      <div className="lg:border-l-2 lg:border-gray-200 lg:pl-4 lg:pr-4">
        {/* Label Skeleton */}
        <div className="w-16 h-4 bg-gray-200 rounded animate-pulse mb-2" />
        <div className="flex items-center space-x-2 mt-1.5">
          {/* Icon Skeleton */}
          <div className="w-5 h-5 bg-gray-200 rounded animate-pulse flex-shrink-0" />
          {/* Input Text Skeleton */}
          <div className="w-20 h-4 bg-gray-200 rounded animate-pulse" />
        </div>
      </div>

      {/* Fifth Column */}
      <div className="lg:border-l-2 lg:border-gray-200 lg:pl-4">
        {/* Label Skeleton */}
        <div className="w-16 h-4 bg-gray-200 rounded animate-pulse mb-2" />
        <div className="flex items-center space-x-2 mt-1.5">
          {/* Icon Skeleton */}
          <div className="w-5 h-5 bg-gray-200 rounded animate-pulse flex-shrink-0" />
          {/* Input Text Skeleton */}
          <div className="w-20 h-4 bg-gray-200 rounded animate-pulse" />
        </div>
      </div>
    </div>
  );
};

export default PrimaryFilterSkeleton;

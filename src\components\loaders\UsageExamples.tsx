'use client';

import { useState, useEffect } from 'react';
import {
  HeaderSkeleton,
  SidebarSkeleton,
  PrimaryFilterSkeleton,
  RecommendationSkeleton,
  ChatSkeleton,
  MainPageSkeleton,
} from './index';

/**
 * Usage Examples Component
 * 
 * Demonstrates different ways to implement skeleton loading
 * in various scenarios and use cases.
 */

// Example 1: Simple Component Loading
export const SimpleLoadingExample = () => {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate API call
    setTimeout(() => setIsLoading(false), 2000);
  }, []);

  return (
    <div className="p-4">
      <h3 className="text-lg font-semibold mb-4">Simple Loading Example</h3>
      {isLoading ? (
        <HeaderSkeleton />
      ) : (
        <div className="bg-blue-100 p-4 rounded">
          <h4>Actual Header Content</h4>
          <p>This content loaded after 2 seconds</p>
        </div>
      )}
    </div>
  );
};

// Example 2: Staggered Loading
export const StaggeredLoadingExample = () => {
  const [loadingStates, setLoadingStates] = useState({
    header: true,
    sidebar: true,
    content: true,
  });

  useEffect(() => {
    // Header loads first
    setTimeout(() => {
      setLoadingStates(prev => ({ ...prev, header: false }));
    }, 1000);

    // Sidebar loads second
    setTimeout(() => {
      setLoadingStates(prev => ({ ...prev, sidebar: false }));
    }, 1500);

    // Content loads last
    setTimeout(() => {
      setLoadingStates(prev => ({ ...prev, content: false }));
    }, 2000);
  }, []);

  return (
    <div className="p-4">
      <h3 className="text-lg font-semibold mb-4">Staggered Loading Example</h3>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <h4 className="font-medium mb-2">Header</h4>
          {loadingStates.header ? (
            <HeaderSkeleton />
          ) : (
            <div className="bg-green-100 p-4 rounded">Header Loaded!</div>
          )}
        </div>
        <div>
          <h4 className="font-medium mb-2">Sidebar</h4>
          {loadingStates.sidebar ? (
            <div className="h-48">
              <SidebarSkeleton />
            </div>
          ) : (
            <div className="bg-yellow-100 p-4 rounded h-48">Sidebar Loaded!</div>
          )}
        </div>
      </div>
    </div>
  );
};

// Example 3: Conditional Loading
export const ConditionalLoadingExample = () => {
  const [dataType, setDataType] = useState<'recommendations' | 'chat' | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const loadData = (type: 'recommendations' | 'chat') => {
    setDataType(type);
    setIsLoading(true);
    setTimeout(() => setIsLoading(false), 2000);
  };

  return (
    <div className="p-4">
      <h3 className="text-lg font-semibold mb-4">Conditional Loading Example</h3>
      <div className="flex gap-2 mb-4">
        <button
          onClick={() => loadData('recommendations')}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Load Recommendations
        </button>
        <button
          onClick={() => loadData('chat')}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
        >
          Load Chat
        </button>
      </div>

      <div className="h-64 border rounded">
        {isLoading ? (
          dataType === 'recommendations' ? (
            <RecommendationSkeleton />
          ) : dataType === 'chat' ? (
            <ChatSkeleton />
          ) : null
        ) : dataType ? (
          <div className="p-4 h-full flex items-center justify-center bg-gray-50">
            <p>{dataType} content loaded successfully!</p>
          </div>
        ) : (
          <div className="p-4 h-full flex items-center justify-center text-gray-500">
            Click a button to load content
          </div>
        )}
      </div>
    </div>
  );
};

// Example 4: Error Handling with Retry
export const ErrorHandlingExample = () => {
  const [state, setState] = useState<'loading' | 'error' | 'success'>('loading');

  useEffect(() => {
    // Simulate API call that might fail
    setTimeout(() => {
      setState(Math.random() > 0.5 ? 'success' : 'error');
    }, 2000);
  }, []);

  const retry = () => {
    setState('loading');
    setTimeout(() => {
      setState(Math.random() > 0.3 ? 'success' : 'error');
    }, 2000);
  };

  return (
    <div className="p-4">
      <h3 className="text-lg font-semibold mb-4">Error Handling Example</h3>
      <div className="h-32 border rounded">
        {state === 'loading' && <PrimaryFilterSkeleton />}
        {state === 'success' && (
          <div className="p-4 h-full flex items-center justify-center bg-green-50">
            <p>Content loaded successfully!</p>
          </div>
        )}
        {state === 'error' && (
          <div className="p-4 h-full flex flex-col items-center justify-center bg-red-50">
            <p className="text-red-600 mb-2">Failed to load content</p>
            <button
              onClick={retry}
              className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
            >
              Retry
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

// Main Usage Examples Component
const UsageExamples = () => {
  return (
    <div className="max-w-6xl mx-auto p-6">
      <h1 className="text-3xl font-bold mb-8">Skeleton Loading Usage Examples</h1>
      
      <div className="space-y-8">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <SimpleLoadingExample />
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <StaggeredLoadingExample />
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <ConditionalLoadingExample />
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <ErrorHandlingExample />
        </div>
      </div>
    </div>
  );
};

export default UsageExamples;

'use client';

import { useState } from 'react';
import AdminLayout from '@/layouts/signinLayout/AdminLayout';
import LandingPage from '@/screen/landing';
import SkeletonDemo from '@/components/loaders/SkeletonDemo';

/**
 * Skeleton Demo Page
 * 
 * This page demonstrates the skeleton loading functionality
 * integrated into the actual application layout.
 */

export default function SkeletonDemoPage() {
  const [showDemo, setShowDemo] = useState(false);
  const [isLayoutLoading, setIsLayoutLoading] = useState(false);
  const [isContentLoading, setIsContentLoading] = useState(false);

  const triggerLayoutSkeleton = () => {
    setIsLayoutLoading(true);
    setTimeout(() => setIsLayoutLoading(false), 3000);
  };

  const triggerContentSkeleton = () => {
    setIsContentLoading(true);
    setTimeout(() => setIsContentLoading(false), 3000);
  };

  if (showDemo) {
    return <SkeletonDemo />;
  }

  return (
    <AdminLayout isLoading={isLayoutLoading}>
      <div className="p-5">
        <div className="bg-white rounded-xl p-6 mb-6">
          <h1 className="text-2xl font-bold mb-4">Skeleton Loading Demo</h1>
          <p className="text-gray-600 mb-6">
            Test the skeleton loading functionality with the buttons below:
          </p>
          
          <div className="flex flex-wrap gap-4 mb-6">
            <button
              onClick={triggerLayoutSkeleton}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              Show Layout Skeleton (3s)
            </button>
            
            <button
              onClick={triggerContentSkeleton}
              className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
            >
              Show Content Skeleton (3s)
            </button>
            
            <button
              onClick={() => setShowDemo(true)}
              className="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
            >
              View All Skeletons
            </button>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-semibold mb-2">Features Demonstrated:</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Complete layout skeleton (header + sidebar + content)</li>
              <li>• Individual component skeletons (filter, chat, recommendations)</li>
              <li>• Staggered loading animation for better UX</li>
              <li>• Responsive design matching actual components</li>
              <li>• Smooth transitions between skeleton and real content</li>
            </ul>
          </div>
        </div>

        {/* Content Area */}
        <LandingPage isLoading={isContentLoading} />
      </div>
    </AdminLayout>
  );
}

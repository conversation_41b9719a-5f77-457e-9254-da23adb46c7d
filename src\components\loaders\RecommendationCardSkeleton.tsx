/**
 * RecommendationCardSkeleton Component
 *
 * Skeleton loader for individual recommendation cards that matches the original layout:
 * - Responsive image with badge overlay
 * - Text content (title, duration, location, tags)
 * - Action button
 * - Adaptive design for different screen sizes
 */

interface RecommendationCardSkeletonProps {
  count?: number; // Number of skeleton cards to show
  showViewAll?: boolean;
}

const RecommendationCardSkeleton = ({
  count = 3,
  showViewAll = true
}: RecommendationCardSkeletonProps) => {
  return (
    <div className="flex flex-col h-full overflow-hidden">
      {/* Cards container with flexible height */}
      <div className="flex flex-col gap-1 sm:gap-2 flex-1 overflow-hidden">
        {Array.from({ length: count }).map((_, index) => (
          <div
            key={index}
            className="bg-white border-none shadow-none p-2 sm:p-4 rounded-xl flex-shrink-0 min-h-[100px] sm:min-h-[120px]"
            data-card="recommendation"
          >
            <div className="flex items-center justify-between rounded-xl w-full h-full">
              {/* Left section: Image and details */}
              <div className="flex gap-2 sm:gap-4 flex-1 min-w-0">
                {/* Image with badge overlay skeleton */}
                <div className="relative flex-shrink-0">
                  <div className="w-16 h-12 sm:w-[120px] sm:h-[95px] bg-gray-200 rounded-xl animate-pulse" />
                  {/* Status badge skeleton positioned over image */}
                  <div className="absolute -bottom-1 right-1 sm:-bottom-2 sm:right-2 w-8 h-3 sm:w-12 sm:h-4 bg-gray-200 rounded-full animate-pulse" />
                </div>

                {/* Text content skeleton */}
                <div className="text-sm space-y-1 sm:space-y-2 flex-1 min-w-0">
                  {/* Title skeleton */}
                  <div className="w-16 sm:w-24 h-3 sm:h-4 bg-gray-200 rounded animate-pulse" />
                  {/* Duration skeleton */}
                  <div className="w-12 sm:w-20 h-2 sm:h-3 bg-gray-200 rounded animate-pulse" />
                  {/* Location skeleton */}
                  <div className="w-10 sm:w-16 h-2 sm:h-3 bg-gray-200 rounded animate-pulse" />
                  {/* Tags skeleton */}
                  <div className="w-20 sm:w-32 h-2 sm:h-3 bg-gray-200 rounded animate-pulse" />
                </div>
              </div>

              {/* Right section: Action button skeleton */}
              <div className="flex-shrink-0 ml-2">
                <div className="w-12 h-5 sm:w-16 sm:h-6 bg-gray-200 rounded-full animate-pulse" />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Optional View All button skeleton */}
      {showViewAll && (
        <div className="flex justify-end mt-1 flex-shrink-0">
          <div className="w-12 h-5 sm:w-16 sm:h-6 bg-gray-200 rounded-full animate-pulse" />
        </div>
      )}
    </div>
  );
};

export default RecommendationCardSkeleton;

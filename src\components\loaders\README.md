# Skeleton Loaders

This directory contains skeleton loader components that match the design and layout of the main application components. These loaders provide a smooth loading experience while data is being fetched.

## 📁 File Structure

```
src/components/loaders/
├── index.ts                      # Export all skeleton loaders
├── README.md                     # This documentation
├── SkeletonDemo.tsx             # Demo page for testing
│
├── Individual Component Skeletons:
├── HeaderSkeleton.tsx           # Header component skeleton
├── SidebarSkeleton.tsx          # Sidebar component skeleton
├── PrimaryFilterSkeleton.tsx    # Primary filter skeleton
├── RecommendationCardSkeleton.tsx # Individual recommendation cards
├── RecommendationSkeleton.tsx   # Complete recommendation section
├── ChatSkeleton.tsx             # Chat section skeleton
│
├── Layout Skeletons:
├── AdminLayoutSkeleton.tsx      # Complete admin layout
├── LandingPageSkeleton.tsx      # Landing page content
└── MainPageSkeleton.tsx         # Complete main page
```

## 🚀 Usage Examples

### Import Individual Skeletons

```tsx
import { HeaderSkeleton, SidebarSkeleton } from '@/components/loaders';

// Use in your component
const MyComponent = () => {
  const [loading, setLoading] = useState(true);
  
  if (loading) {
    return <HeaderSkeleton />;
  }
  
  return <Header />;
};
```

### Import Complete Page Skeleton

```tsx
import { MainPageSkeleton } from '@/components/loaders';

// Use for complete page loading
const HomePage = () => {
  const [loading, setLoading] = useState(true);
  
  if (loading) {
    return <MainPageSkeleton />;
  }
  
  return <MainPage />;
};
```

### Import Section Skeletons

```tsx
import { LandingPageSkeleton, RecommendationSkeleton } from '@/components/loaders';

// Use for specific sections
const Dashboard = () => {
  const [dataLoading, setDataLoading] = useState(true);
  
  return (
    <div>
      {dataLoading ? (
        <RecommendationSkeleton cardCount={5} />
      ) : (
        <RecommendationSection />
      )}
    </div>
  );
};
```

## 🎨 Component Details

### Individual Component Skeletons

- **HeaderSkeleton**: Matches header layout with search bar, buttons, and mobile responsive design
- **SidebarSkeleton**: Includes logo, menu sections, navigation items, and profile section
- **PrimaryFilterSkeleton**: 5-column grid layout with labels and input fields
- **RecommendationCardSkeleton**: Individual cards with image, text content, and action buttons
- **ChatSkeleton**: Chat interface with message bubbles and input area

### Section Skeletons

- **RecommendationSkeleton**: Complete recommendation section with header and cards
- **LandingPageSkeleton**: Full landing page content including filters and grid layout

### Layout Skeletons

- **AdminLayoutSkeleton**: Complete admin layout with sidebar and header
- **MainPageSkeleton**: Combines AdminLayout + LandingPage for complete page loading

## 🔧 Customization

### Adjusting Card Count

```tsx
// Show different number of skeleton cards
<RecommendationCardSkeleton count={5} showViewAll={false} />
<RecommendationSkeleton cardCount={4} />
```

### Custom Styling

All skeletons use Tailwind CSS classes and can be customized:

```tsx
// Wrap in custom container
<div className="custom-skeleton-container">
  <HeaderSkeleton />
</div>
```

## 🧪 Testing with Demo

Use the `SkeletonDemo` component to test all skeleton loaders:

```tsx
import SkeletonDemo from '@/components/loaders/SkeletonDemo';

// Add to your development routes
<SkeletonDemo />
```

## 📱 Responsive Design

All skeleton loaders are fully responsive and match the responsive behavior of their corresponding components:

- Mobile-first design approach
- Proper spacing and sizing across screen sizes
- Hidden/shown elements based on breakpoints

## ⚡ Performance

- Lightweight components with minimal DOM elements
- CSS animations using `animate-pulse` for smooth loading effects
- No external dependencies beyond Tailwind CSS

## 🎯 Best Practices

1. **Match Loading Time**: Use skeletons that approximate the actual loading time
2. **Consistent Design**: Ensure skeleton dimensions match actual content
3. **Smooth Transitions**: Implement fade-in effects when replacing skeletons
4. **Accessibility**: Maintain proper ARIA labels and screen reader support

## 🔄 Integration with Data Fetching

```tsx
// Example with React Query
const { data, isLoading } = useQuery('recommendations', fetchRecommendations);

return (
  <div>
    {isLoading ? (
      <RecommendationSkeleton />
    ) : (
      <RecommendationSection data={data} />
    )}
  </div>
);
```

## 📋 Maintenance

When updating the original components:
1. Update corresponding skeleton loaders to match new layouts
2. Test responsive behavior across different screen sizes
3. Verify animation performance and smoothness
4. Update documentation if new props are added

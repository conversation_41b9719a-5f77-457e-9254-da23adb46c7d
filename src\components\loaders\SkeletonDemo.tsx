'use client';

import { useState } from 'react';
import {
  HeaderSkeleton,
  SidebarSkeleton,
  PrimaryFilterSkeleton,
  RecommendationCardSkeleton,
  RecommendationSkeleton,
  ChatSkeleton,
  AdminLayoutSkeleton,
  LandingPageSkeleton,
  MainPageSkeleton,
} from './index';

/**
 * SkeletonDemo Component
 * 
 * Demo page to showcase all skeleton loaders.
 * Useful for testing and development purposes.
 */

const SkeletonDemo = () => {
  const [selectedSkeleton, setSelectedSkeleton] = useState('main');

  const skeletonOptions = [
    { key: 'main', label: 'Complete Main Page', component: <MainPageSkeleton /> },
    { key: 'landing', label: 'Landing Page Only', component: <LandingPageSkeleton /> },
    { key: 'layout', label: 'Admin Layout Only', component: <AdminLayoutSkeleton /> },
    { key: 'header', label: 'Header Only', component: <HeaderSkeleton /> },
    { key: 'sidebar', label: 'Sidebar Only', component: <SidebarSkeleton /> },
    { key: 'filter', label: 'Primary Filter Only', component: <PrimaryFilterSkeleton /> },
    { key: 'recommendation', label: 'Recommendation Section', component: <RecommendationSkeleton /> },
    { key: 'cards', label: 'Recommendation Cards', component: <RecommendationCardSkeleton count={3} /> },
    { key: 'chat', label: 'Chat Section', component: <ChatSkeleton /> },
  ];

  const currentSkeleton = skeletonOptions.find(option => option.key === selectedSkeleton);

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Demo Controls */}
      <div className="bg-white border-b border-gray-200 p-4">
        <h1 className="text-2xl font-bold mb-4">Skeleton Loaders Demo</h1>
        <div className="flex flex-wrap gap-2">
          {skeletonOptions.map((option) => (
            <button
              key={option.key}
              onClick={() => setSelectedSkeleton(option.key)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedSkeleton === option.key
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>

      {/* Skeleton Display */}
      <div className="p-4">
        <h2 className="text-lg font-semibold mb-4">
          Current: {currentSkeleton?.label}
        </h2>
        <div className="bg-white rounded-lg shadow-sm overflow-hidden">
          {currentSkeleton?.component}
        </div>
      </div>
    </div>
  );
};

export default SkeletonDemo;

/**
 * HeaderSkeleton Component
 * 
 * Skeleton loader for the Header component that matches the original layout:
 * - Mobile: Logo + Sidebar toggle button + Login button
 * - Desktop: Search bar + Settings button + Login button
 */

const HeaderSkeleton = () => {
  return (
    <header className="w-full flex justify-between items-center px-6 py-3 max-md:px-3 bg-white z-20 relative">
      {/* Mobile Section - Logo + Sidebar Toggle */}
      <div className="md:hidden mr-3 flex flex-row gap-3">
        {/* Logo Skeleton */}
        <div className="w-9 h-9 bg-gray-200 rounded-md animate-pulse" />
        {/* Sidebar Toggle Button Skeleton */}
        <div className="w-8 h-8 bg-gray-200 rounded-md animate-pulse" />
      </div>

      {/* Search Input Skeleton - Hidden on Mobile */}
      <div className="flex items-center bg-gray-100 rounded-full pl-4 pr-1 h-12 w-full max-w-md max-md:hidden">
        <div className="flex-1 h-4 bg-gray-200 rounded animate-pulse" />
        <div className="h-10 w-10 rounded-full bg-gray-200 animate-pulse" />
      </div>

      {/* Right Section */}
      <div className="flex items-center max-md:justify-end max-md:items-end gap-4">
        {/* Settings Button Skeleton - Hidden on Mobile */}
        <div className="h-10 w-10 rounded-full bg-gray-200 animate-pulse max-md:hidden" />
        {/* Login Button Skeleton */}
        <div className="h-9 w-24 rounded-full bg-gray-200 animate-pulse" />
      </div>
    </header>
  );
};

export default HeaderSkeleton;
